import React from 'react';
import { Plane } from 'lucide-react';

const SectionBadge = ({ text, isMobile = false }) => {
  return (
    <div className={`inline-flex items-center bg-[#24BDC7] text-white w-fit py-1 sm:py-1.5 md:py-2 px-2 sm:px-3 md:px-4 ${
      isMobile
        ? "text-xs sm:text-sm py-1 pr-3 sm:pr-4 pl-2 mb-4 sm:mb-6"
        : "text-xs sm:text-sm md:text-sm py-1 pr-3 sm:pr-4 md:pr-5 pl-2 mb-4 sm:mb-6 md:mb-8"
    } font-semibold rounded-full shadow-lg transition-all duration-300 hover:shadow-xl`}>
      <Plane
        className="bg-white rounded-full p-0.5 sm:p-1 mr-1.5 sm:mr-2 text-[#24BDC7] rotate-45"
        size={isMobile ? 14 : 16}
      />
      {text}
    </div>
  );
};

export default SectionBadge;
