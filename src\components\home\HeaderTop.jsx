import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { Facebook, Twitter, Instagram, Linkedin, Phone, Mail, User, UserPlus, ChevronDown } from 'lucide-react';

const HeaderTop = () => {
  const [languageOpen, setLanguageOpen] = useState(false);
  const [currencyOpen, setCurrencyOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('ENG');
  const [selectedCurrency, setSelectedCurrency] = useState('USD');

  const languageRef = useRef(null);
  const currencyRef = useRef(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (languageRef.current && !languageRef.current.contains(event.target)) {
        setLanguageOpen(false);
      }
      if (currencyRef.current && !currencyRef.current.contains(event.target)) {
        setCurrencyOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const languages = [
    { code: 'ENG', label: 'ENG' },
    { code: 'UR', label: 'UR' }
  ];

  const currencies = [
    { code: 'USD', label: 'USD' },
    { code: 'PKR', label: 'PKR' }
  ];

  return (
    <div className='hidden lg:block'>
      <div className="relative w-full text-white text-xs lg:text-sm py-2 px-4 lg:px-10 flex flex-col lg:flex-row lg:justify-between items-center z-10  border-b-[0.5px] border-[#757F95] ">
        {/* Content */}
        <div className="relative z-10 flex flex-col lg:flex-row lg:justify-between items-center w-full">
          {/* Left: Social Icons */}
          <div className="hidden lg:flex items-center gap-4">
            <a href="#" className="hidden xl:block text-white hover:text-[#24bdc7] hover:bg-white border-2 border-white rounded-full p-2">
              <Facebook size={13} />
            </a>
            <a href="#" className="hidden xl:block text-white hover:text-[#24bdc7] hover:bg-white border-2 border-white rounded-full p-2">
              <Twitter size={13} />
            </a>
            <a href="#" className="hidden xl:block text-white hover:text-[#24bdc7] hover:bg-white border-2 border-white rounded-full p-2">
              <Instagram size={13} />
            </a>
            <a href="#" className="hidden xl:block text-white hover:text-[#24bdc7] hover:bg-white border-2 border-white rounded-full p-2">
              <Linkedin size={13} />
            </a>
            <span className="ml-4 lg:flex items-center font-semibold text-[16px] gap-2 hover:text-[#24bdc7]">
              <Phone size={16} /> ****** 4567 897
            </span>
            <span className="ml-4 lg:flex items-center font-semibold text-[16px] gap-2 hover:text-[#24bdc7]">
              <Mail size={16} /> <EMAIL>
            </span>
          </div>

          {/* Right: Language, Currency, Auth, Button */}
          <div className="flex items-center gap-2 lg:gap-4 font-semibold text-[12px] lg:text-[16px] mt-2 lg:mt-0">
            {/* Custom Language Dropdown */}
            <div className="relative" ref={languageRef}>
              <button
                onClick={() => {
                  setLanguageOpen(!languageOpen);
                  setCurrencyOpen(false);
                }}
                className="flex items-center gap-1 px-2 py-1 text-white hover:bg-white/10 rounded-md transition-colors duration-200 font-semibold"
              >
                {selectedLanguage}
                <ChevronDown size={14} className={`transition-transform duration-200 ${languageOpen ? 'rotate-180' : ''}`} />
              </button>

              {languageOpen && (
                <div className="absolute top-full mt-1 bg-white text-gray-800 rounded-md shadow-lg border border-gray-200 py-1 z-50 ">
                  {languages.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => {
                        setSelectedLanguage(lang.code);
                        setLanguageOpen(false);
                      }}
                      className="w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors duration-150 text-sm"
                    >
                      {lang.label}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Custom Currency Dropdown */}
            <div className="relative" ref={currencyRef}>
              <button
                onClick={() => {
                  setCurrencyOpen(!currencyOpen);
                  setLanguageOpen(false);
                }}
                className="flex items-center gap-1 px-2 py-1 text-white hover:bg-white/10 rounded-md transition-colors duration-200 font-semibold"
              >
                {selectedCurrency}
                <ChevronDown size={14} className={`transition-transform duration-200 ${currencyOpen ? 'rotate-180' : ''}`} />
              </button>

              {currencyOpen && (
                <div className="absolute top-full mt-1 bg-white text-gray-800 rounded-md shadow-lg border border-gray-200 py-1 z-50">
                  {currencies.map((currency) => (
                    <button
                      key={currency.code}
                      onClick={() => {
                        setSelectedCurrency(currency.code);
                        setCurrencyOpen(false);
                      }}
                      className="w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors duration-150 text-sm"
                    >
                      {currency.label}
                    </button>
                  ))}
                </div>
              )}
            </div>

            <Link href="/login" className="hover:underline hover:text-[#24bdc7] flex items-center gap-1">
              <User size={16} /> Login
            </Link>
            <Link href="/signup" className="hover:underline hover:text-[#24bdc7] flex items-center gap-1">
              <UserPlus size={16} /> Sign Up
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeaderTop;