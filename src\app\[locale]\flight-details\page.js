"use client"
import Header from "@/components/home/<USER>";
import { FlightDetailPage } from "@/components/bookingDetail";
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "next/navigation";
import { fetchFlightDetail } from "@/redux/slices/flightDetailSlice";

export default function FlightDetailsPage() {
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const flightDetail = useSelector((state) => state.flightDetail.detail);

  // Get flightId from URL parameters
  const flightId = searchParams.get('flightId') || searchParams.get('fareId') || 'fare-0001';

  useEffect(() => {
    dispatch(fetchFlightDetail(flightId));
  }, [dispatch, flightId]);

  useEffect(() => {
    if (flightDetail) {
      console.log("Flight detail data from Redux store:", flightDetail);
    }
  }, [flightDetail]);

  const breadcrumbItems = [
    { label: 'Flight Details', href: '/flight-details' }
  ];

  // Normalize flight data from API or fallback
  const flightData = useMemo(() => ({
    image: flightDetail?.image || "/assets/img/flight/01.jpg",
    route: flightDetail?.route || null,
    type: flightDetail?.type || null,
    rating: flightDetail?.rating ?? null,
    reviews: flightDetail?.reviews ?? null,
    takeOffTime: flightDetail?.takeOffTime || null,
    takeOffCode: flightDetail?.takeOffCode || null,
    landingTime: flightDetail?.landingTime || null,
    landingCode: flightDetail?.landingCode || null,
    stops: flightDetail?.stops || null,
    stopDuration: flightDetail?.stopDuration || null,
    airline: flightDetail?.airline || null,
    flightType: flightDetail?.flightType || null,
    fareType: flightDetail?.fareType || null,
    cancellationFee: flightDetail?.cancellationFee || null,
    flightChange: flightDetail?.flightChange || null,
    seatsSequence: flightDetail?.seatsSequence || null,
    inflightFeatures: flightDetail?.inflightFeatures || null,
    taxesFees: flightDetail?.taxesFees || null,
  }), [flightDetail]);

  // Normalize aboutData
  const aboutData = useMemo(() => ({
    title: flightDetail?.about?.title || "About Airline",
    content: flightDetail?.about?.content || []
  }), [flightDetail]);

  return (
    <>
      <Header
        backgroundImage="/assets/img/breadcrumb/01.jpg"
        height="min-h-[50vh]"
        heroTitle="Flight Details"
        heroSubtitle="Complete information about your selected flight"
        showBooking={false}
        breadcrumbItems={breadcrumbItems}
        className="-mt-50"
      />

      <FlightDetailPage
        flightData={flightData}
        aboutData={aboutData}
        flightId={flightId}
      />
    </>
  );
}
