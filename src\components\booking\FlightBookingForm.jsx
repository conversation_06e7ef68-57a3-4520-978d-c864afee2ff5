"use client";
import React, { useState } from 'react';
import PersonalInfoForm from './PersonalInfoForm';
import PaymentForm from './PaymentForm';
import BookingSummary from './BookingSummary';

const FlightBookingForm = () => {
  const [bookingData, setBookingData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address1: '',
    address2: '',
    country: '',
    age: '',
    city: '',
    state: '',
    zipCode: '',
    additionalInfo: '',
    
    // Payment Information
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardHolderName: '',

    // PayPal Information
    paypalEmail: '',
    paypalPassword: '',

    // Payoneer Information
    payoneerEmail: '',
    payoneerPassword: ''
  });

  // Mock flight data
  const flightData = {
    route: 'New York – Los Angeles',
    type: 'One Way Flight',
    rating: 4.5,
    reviews: 35,
    takeOff: 'New York',
    landing: 'Los Angeles',
    journeyDate: '20 Aug 2025 at 10:10 AM',
    airline: 'Delta',
    flightType: 'One Way',
    flightClass: 'Economy',
    flightDuration: '4h 05m',
    flightStop: 'Non Stop',
    adults: 4,
    children: 2,
    subTotal: '$50,540.00',
    discount: '$600.00',
    taxes: '$560.00',
    youPay: '$51,543.00'
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setBookingData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Booking submitted:', bookingData);
    // Handle booking submission
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-20">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Booking Form */}
        <div className="lg:col-span-2 space-y-6">
          <PersonalInfoForm
            formData={bookingData}
            onChange={handleInputChange}
          />

          <PaymentForm
            formData={bookingData}
            onChange={handleInputChange}
            onSubmit={handleSubmit}
          />
        </div>

        {/* Right Column - Booking Summary */}
        <div className="lg:col-span-1">
          <BookingSummary flightData={flightData} onConfirmBooking={handleSubmit} />
        </div>
      </div>
    </div>
  );
};

export default FlightBookingForm;
