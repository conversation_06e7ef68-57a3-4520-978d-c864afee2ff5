"use client";
import React from 'react';

const AboutSection = ({ 
  title = "About Delta Airlines",
  content = [],
  className = ""
}) => {
  const defaultContent = [
    "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text.",
    "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text."
  ];

  const paragraphs = content.length > 0 ? content : defaultContent;

  return (
    <div className={`p-2 ${className}`}>
      <h3 className="text-xl font-bold text-[#0C2C7A] mb-6">{title}</h3>
      
      <div className="space-y-4">
        {paragraphs.map((paragraph, index) => (
          <p key={index} className="text-gray-600 text-sm leading-relaxed">
            {paragraph}
          </p>
        ))}
      </div>
    </div>
  );
};

export default AboutSection;
