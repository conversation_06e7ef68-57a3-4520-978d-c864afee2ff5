"use client";
import React from 'react';
import { FileText, Plane, RotateCcw, DollarSign, AlertTriangle, X, Check } from 'lucide-react';

const FareRules = ({ 
  title = "Fare Rules For Your Flight",
  rules = [],
  className = ""
}) => {
  const defaultRules = [
    { icon: Check, label: "Rules and Policies", color: "bg-[#24BDC7]" },
    { icon: Check, label: "Flight Changes", color: "bg-[#24BDC7]" },
    { icon: Check, label: "Refunds", color: "bg-[#24BDC7]" },
    { icon: Check, label: "Airline Penalties", color: "bg-[#24BDC7]" },
    { icon: Check, label: "Flight Cancellation", color: "bg-[#24BDC7]" },
    { icon: Check, label: "Airline Terms Of Use", color: "bg-[#24BDC7]" }
  ];

  const rulesList = rules.length > 0 ? rules : defaultRules;

  return (
    <div className={` p-2 ${className}`}>
      <h3 className="text-xl font-bold text-[#0C2C7A] mb-6">{title}</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {rulesList.map((rule, index) => {
          const IconComponent = rule.icon;
          
          return (
            <div 
              key={index} 
              className="flex items-center space-x-3 p-4 transition-all cursor-pointer"
            >
              <div className={`w-10 h-10 ${rule.color} rounded-full flex items-center justify-center flex-shrink-0`}>
                <IconComponent size={18} className="text-white" />
              </div>
              <span className="text-sm font-medium text-[#0C2C7A]">
                {rule.label}
              </span>
            </div>
          );
        })}
      </div>

      {/* Additional Information */}
      {/* <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="text-blue-600 mt-1 flex-shrink-0" size={18} />
          <div>
            <h4 className="text-sm font-semibold text-blue-800 mb-1">Important Notice</h4>
            <p className="text-sm text-blue-700">
              Please review all fare rules and policies before booking. Changes and cancellations may be subject to airline fees and restrictions.
            </p>
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default FareRules;
