'use client';
import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import FormInput from '@/components/ui/FormInput';
import AuthButton from '@/components/ui/AuthButton';
import SocialButton from '@/components/ui/SocialButton';
import Checkbox from '@/components/ui/Checkbox';

const SignupForm = () => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    agreeToTerms: false
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field) => (e) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleCheckboxChange = (e) => {
    setFormData(prev => ({
      ...prev,
      agreeToTerms: e.target.checked
    }));
    // Clear error when user checks the box
    if (errors.agreeToTerms) {
      setErrors(prev => ({
        ...prev,
        agreeToTerms: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'Full name must be at least 2 characters';
    }
    
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = 'You must agree to the Terms of Service';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Set login cookie (as per middleware requirements)
      document.cookie = 'user_logged_in=true; path=/; max-age=86400'; // 24 hours
      
      // Redirect to dashboard or home
      router.push('/');
    } catch (error) {
      setErrors({ submit: 'Signup failed. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const handleSocialSignup = (provider) => {
    console.log(`Signup with ${provider}`);
    // Implement social signup logic here
  };

  return (
    <div className="w-full max-w-md mx-auto bg-white rounded-3xl shadow-2xl p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="mb-4">
          <Image
            src="/assets/img/logo/logo-dark.png"
            alt="Tavelo"
            width={150}
            height={50}
            className="mx-auto"
          />
        </div>
        <h3 className="text-lg font-bold text-[#24BDC7] mb-2">
          Create your tavelo account
        </h3>
      </div>

      {/* Signup Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Full Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Full Name
          </label>
          <FormInput
            type="text"
            placeholder="Your Name"
            value={formData.fullName}
            onChange={handleInputChange('fullName')}
            icon="user"
            error={errors.fullName}
            required
          />
        </div>

        {/* Email Address */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Address
          </label>
          <FormInput
            type="email"
            placeholder="Your Email"
            value={formData.email}
            onChange={handleInputChange('email')}
            icon="email"
            error={errors.email}
            required
          />
        </div>

        {/* Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Password
          </label>
          <FormInput
            type="password"
            placeholder="Your Password"
            value={formData.password}
            onChange={handleInputChange('password')}
            icon="password"
            error={errors.password}
            required
          />
        </div>

        {/* Terms Agreement */}
        <div>
          <Checkbox
            checked={formData.agreeToTerms}
            onChange={handleCheckboxChange}
            error={errors.agreeToTerms}
            label={
              <span>
                I agree with the{' '}
                <Link 
                  href="/terms-of-service" 
                  className="text-[#24BDC7] hover:underline"
                >
                  Terms Of Service
                </Link>
              </span>
            }
            required
          />
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <p className="text-sm text-red-500 text-center">{errors.submit}</p>
        )}

        {/* Signup Button */}
        <AuthButton
          type="submit"
          loading={loading}
          className="mb-6"
        >
           Sign Up
        </AuthButton>

        {/* Divider */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or</span>
          </div>
        </div>

        {/* Social Signup Buttons */}
        <div className="space-y-3">
          <SocialButton
            provider="facebook"
            onClick={() => handleSocialSignup('facebook')}
          >
            Login With Facebook
          </SocialButton>
          
          <SocialButton
            provider="google"
            onClick={() => handleSocialSignup('google')}
          >
            Login With Google
          </SocialButton>
        </div>

        {/* Login Link */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <Link 
              href="/login" 
              className="text-[#24BDC7] hover:underline font-medium"
            >
              Login
            </Link>
          </p>
        </div>
      </form>
    </div>
  );
};

export default SignupForm;
