"use client";
import React from 'react';
import { useSelector } from 'react-redux';
import FlightCard from './FlightCard';
import AboutSection from './AboutSection';
import InflightFeatures from './InflightFeatures';
import BaggageSection from './BaggageSection';
import FareRules from './FareRules';
import Sidebar from './DetailSidebar';
import FAQ from './FAQ';
import ReviewForm from './ReviewForm';
import Reviews from './Reviews';

const FlightDetailPage = ({
  flightData = {},
  aboutData = {},
  sidebarData = {},
  className = ""
}) => {
  // 🔹 Get Redux flight detail
  const flightDetail = useSelector((state) => state.flightDetail.detail);

  // 🔹 Default flight data (fallbacks)
  const defaultFlightData = {
    images: [
      "/assets/img/flight/single-1.jpg",
      "/assets/img/flight/single-2.jpg",
      "/assets/img/flight/single-3.jpg"
    ],
    route: "New York - Los Angeles",
    type: "One Way",
    rating: 4.5,
    reviews: 35,
    takeOffTime: "Sat, 25 Oct | 07:30AM",
    takeOffCode: "JFK",
    landingTime: "Sat, 25 Oct | 09:25AM",
    landingCode: "LAX",
    stops: "1 Stop (STN)",
    stopDuration: "1h 25m",
    airline: "Delta Airlines",
    flightType: "Economy",
    fareType: "Refundable",
    cancellationFee: "$50 / Per Person",
    flightChange: "$50 / Per Person",
    seatsSequence: "$50 Extra Charge",
    inflightFeatures: "Available",
    taxesFees: "$50"
  };

  // 🔹 Map API response -> component data
  const apiFlightData = flightDetail
    ? {
        images: flightDetail?.segments?.[0]?.images || [],
        route: `${flightDetail?.segments?.[0]?.departure?.city} - ${flightDetail?.segments?.[0]?.arrival?.city}`,
        type: flightDetail?.flightType || "N/A",
        rating: flightDetail?.segments?.[0]?.airlineRating || 0,
        reviews: flightDetail?.segments?.[0]?.reviews || 0,
        takeOffTime: flightDetail?.segments?.[0]?.departure?.scheduledDeparture || "N/A",
        takeOffCode: flightDetail?.segments?.[0]?.departure?.cityCode || "",
        landingTime: flightDetail?.segments?.[0]?.arrival?.scheduledArrival || "N/A",
        landingCode: flightDetail?.segments?.[0]?.arrival?.cityCode || "",
        stops: `${flightDetail?.segments?.length - 1} Stop(s)`,
        airline: flightDetail?.segments?.[0]?.airlineDisplayName || "N/A",
        flightType: flightDetail?.serviceClass || "N/A",
        fareType: flightDetail?.refundable || "N/A",
        taxesFees: flightDetail?.tax ? `$${flightDetail.tax}` : "N/A",
        price: flightDetail?.price ? `$${flightDetail.price}` : "N/A",
      }
    : {};

  // 🔹 Merge: defaults + API + props
  const flight = { ...defaultFlightData, ...apiFlightData, ...flightData };

  // 🔹 About section
  const defaultAboutData = {
    title: "About Delta Airlines",
    content: [
      "There are many variations of passages of Lorem Ipsum available...",
      "More details about the airline and services..."
    ]
  };
  const about = { ...defaultAboutData, ...aboutData };

  // 🔹 Sidebar fallback
  const defaultSidebarData = {
    faqData: [
      { question: "What Are The Charges Of Services ?", answer: "We denounce with righteous indignation..." },
      { question: "How Can I Become A Member ?", answer: "Simply sign up on our website..." }
    ],
    contactData: {
      title: "Get A Question?",
      description: "It is a long established fact...",
      phone: "****** 4567 897",
      email: "<EMAIL>"
    }
  };
  const sidebar = { ...defaultSidebarData, ...sidebarData };

  return (
    <div className={`min-h-screen mb-20 ${className}`}>
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            <FlightCard
              flightData={flight}
              onPrevious={() => console.log('Previous flight')}
              onNext={() => console.log('Next flight')}
            />
            <hr className="border-gray-200 px-5" />

            <AboutSection title={about.title} content={about.content} />
            <hr className="border-gray-200 px-5" />

            <InflightFeatures />
            <hr className="border-gray-200 px-5" />

            <BaggageSection />
            <hr className="border-gray-200 px-5" />

            <FareRules />
            <hr className="border-gray-200 px-5" />

            <FAQ />
            <hr className="border-gray-200 px-5" />

            <Reviews />
            <ReviewForm />
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Sidebar {...sidebar} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightDetailPage;
