"use client";
import React, { useState } from 'react';
import { Calendar, Users, Heart, Share2, Eye, Calendar1Icon, PersonStandingIcon, User2 } from 'lucide-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCalendarDays } from '@fortawesome/free-solid-svg-icons';

const BookingWidget = ({ 
  price = "$450.00",
  originalPrice = "$500.00",
  journeyDate = "9/11/2025",
  journeyDay = "Thursday",
  returnDate = "9/12/2025",
  returnDay = "Friday",
  passengers = "2 Passenger",
  passengerClass = "Business",
  views = "250 Views",
  shares = "4 Share",
  onBookNow,
  onAddToWishlist
}) => {
  const [isWishlisted, setIsWishlisted] = useState(false);

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted);
    if (onAddToWishlist) {
      onAddToWishlist(!isWishlisted);
    }
  };

  return (
   <div className="bg-white rounded-3xl shadow-lg p-6">
          <div className="mb-4">
            <span className="text-md text-[#24BDC7] uppercase tracking-wide font-bold">BESTSELLER</span>
            <div className="flex items-center mt-1 gap-2">
              <span className="text-sm text-gray-500">From </span>
               <span className="text-lg font-bold text-red-500">{price}</span>
              <span className="text-sm text-gray-500 line-through">{originalPrice}</span>
             
            </div>
          </div>

          {/* Journey Date */}
          <div className="space-y-4 mb-6">
            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-3xl">
              <div>
                <p className="text-xs text-gray-500 uppercase">Journey Date</p>
                <p className="font-semibold text-[#0C2C7A]">9/11/2025</p>
                <p className="text-sm text-gray-600">Thursday</p>
              </div>
              <div className="w-8 h-8 flex items-center justify-center">
                <span className="text-xs">
                   <FontAwesomeIcon size='xl' icon={faCalendarDays} className="text-[#24BDC7] text-sm" />
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-3xl">
              <div>
                <p className="text-xs text-gray-500 uppercase">Return Date</p>
                <p className="font-semibold text-[#0C2C7A]">9/12/2025</p>
                <p className="text-sm text-gray-600">Friday</p>
              </div>
              <div className="w-8 h-8 rounded flex items-center justify-center">
                <span className="text-xs">
                <FontAwesomeIcon size='xl' icon={faCalendarDays} className="text-[#24BDC7] text-sm" />
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-3xl">
              <div>
                <p className="text-xs text-gray-500 uppercase">Passenger Class</p>
                <p className="font-semibold text-[#0C2C7A]">2 Passenger</p>
                <p className="text-sm text-gray-600">Business</p>
              </div>
              <div className="w-8 h-8 flex items-center justify-center">
                <span className="text-xs">
                  <User2 size={20} className="text-[#24BDC7]" />
                </span>
              </div>
            </div>
          </div>

          {/* Book Now Button */}
          <button className="w-full bg-[#24BDC7] text-white py-3 px-6 rounded-3xl font-semibold hover:bg-[#1ea5ae] transition-colors mb-4">
            Book Now
          </button>

          {/* Add to Wishlist */}
          <button className="w-full border border-[#24BDC7] text-[#24BDC7] py-3 px-6 rounded-3xl font-semibold hover:bg-[#24BDC7] hover:text-white transition-colors">
            Add To Wishlist
          </button>

          {/* Stats */}
          <div className="flex justify-between text-center mt-6 pt-4 border-t border-gray-200">
            <div>
              <p className="text-sm font-semibold text-[#0C2C7A]">250 Views</p>
            </div>
            <div>
              <p className="text-sm font-semibold text-[#0C2C7A]">4 Share</p>
            </div>
          </div>
        </div>
  );
};

export default BookingWidget;
