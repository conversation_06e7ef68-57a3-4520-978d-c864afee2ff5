# Sidebar Components Documentation

Professional sidebar components for travel booking and information display.

## 🎯 Components Overview

### 1. **FAQ Component**
Expandable FAQ section with question/answer pairs.

```jsx
import { FAQ } from '@/components/ui';

<FAQ 
  title="Frequently Asked Questions"
  faqs={[
    {
      question: "What Are The Charges Of Services ?",
      answer: "We denounce with righteous indignation..."
    }
  ]}
/>
```

### 2. **ContactCard Component**
Contact information display with phone and email.

```jsx
import { ContactCard } from '@/components/ui';

<ContactCard 
  title="Get A Question?"
  description="Contact us for any queries"
  phone="****** 4567 897"
  email="<EMAIL>"
/>
```

### 3. **OrganizedBy Component**
Organizer profile with avatar and contact button.

```jsx
import { OrganizedBy } from '@/components/ui';

<OrganizedBy 
  organizerName="Roltak Travel Agency"
  memberSince="Member Since 2025"
  avatar="/path/to/avatar.jpg"
  onSendMessage={() => console.log('Message sent')}
/>
```

### 4. **BookingWidget Component**
Complete booking interface with pricing and dates.

```jsx
import { BookingWidget } from '@/components/ui';

<BookingWidget 
  price="$450.00"
  originalPrice="$500.00"
  journeyDate="9/11/2025"
  returnDate="9/12/2025"
  passengers="2 Passenger"
  onBookNow={() => console.log('Book now')}
/>
```

### 5. **WhyBookWithUs Component**
Feature list with icons and descriptions.

```jsx
import { WhyBookWithUs } from '@/components/ui';

<WhyBookWithUs 
  features={[
    {
      icon: Shield,
      title: "Best Price Guarantee",
      description: "We guarantee the best prices"
    }
  ]}
/>
```

### 6. **SidebarWidgets Component**
Container component that combines all sidebar widgets.

```jsx
import { SidebarWidgets } from '@/components/ui';

<SidebarWidgets 
  showFAQ={true}
  showContact={true}
  showOrganizer={true}
  showBooking={true}
  showWhyBook={true}
  faqData={faqData}
  contactData={contactData}
  organizerData={organizerData}
  bookingData={bookingData}
  whyBookData={whyBookData}
/>
```

## 🎨 Design Features

- **Consistent Styling**: All components use brand colors (#24BDC7, #0C2C7A)
- **Responsive Design**: Mobile-first approach with responsive layouts
- **Interactive Elements**: Hover effects, click states, animations
- **Icon Integration**: Lucide React icons throughout
- **Professional Look**: Clean, modern design matching travel industry standards

## 📱 Responsive Behavior

- **Mobile**: Single column layout, touch-friendly buttons
- **Tablet**: Optimized spacing and typography
- **Desktop**: Full sidebar layout with proper spacing

## 🔧 Customization

All components accept props for:
- Custom titles and descriptions
- Dynamic data
- Event handlers
- Styling overrides
- Show/hide functionality

## 🚀 Usage Example

```jsx
// Complete sidebar implementation
import { SidebarWidgets } from '@/components/ui';

function FlightDetailsPage() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className="lg:col-span-2">
        {/* Main content */}
      </div>
      <div className="lg:col-span-1">
        <SidebarWidgets 
          showFAQ={true}
          showContact={true}
          showOrganizer={true}
          showBooking={true}
          showWhyBook={true}
          // ... data props
        />
      </div>
    </div>
  );
}
```
