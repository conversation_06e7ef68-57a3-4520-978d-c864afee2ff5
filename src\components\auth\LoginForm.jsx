'use client';
import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import FormInput from '@/components/ui/FormInput';
import AuthButton from '@/components/ui/AuthButton';
import SocialButton from '@/components/ui/SocialButton';
import Checkbox from '@/components/ui/Checkbox';

const LoginForm = () => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field) => (e) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleCheckboxChange = (e) => {
    setFormData(prev => ({
      ...prev,
      rememberMe: e.target.checked
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Set login cookie (as per middleware requirements)
      document.cookie = 'user_logged_in=true; path=/; max-age=86400'; // 24 hours
      
      // Redirect to dashboard or home
      router.push('/');
    } catch (error) {
      setErrors({ submit: 'Login failed. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = (provider) => {
    console.log(`Login with ${provider}`);
    // Implement social login logic here
  };

  return (
    <div className="w-full max-w-md mx-auto bg-white rounded-4xl shadow-2xl p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="mb-4">
          <Image
            src="/assets/img/logo/logo-dark.png"
            alt="Tavelo"
            width={150}
            height={50}
            className="mx-auto"
          />
        </div>
        <h3 className="text-lg font-bold text-[#24BDC7] mb-2">
          Login with your tavelo account
        </h3>
      </div>

      {/* Login Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Email Address */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Address
          </label>
          <FormInput
            type="email"
            placeholder="Your Email"
            value={formData.email}
            onChange={handleInputChange('email')}
            icon="email"
            error={errors.email}
            required
          />
        </div>

        {/* Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Password
          </label>
          <FormInput
            type="password"
            placeholder="Your Password"
            value={formData.password}
            onChange={handleInputChange('password')}
            icon="password"
            error={errors.password}
            required
          />
        </div>

        {/* Remember Me & Forgot Password */}
        <div className="flex items-center justify-between">
          <Checkbox
            checked={formData.rememberMe}
            onChange={handleCheckboxChange}
            label="Remember Me"
          />
          <Link 
            href="/forgot-password" 
            className="text-sm text-[#24BDC7] hover:underline"
          >
            Forgot Password?
          </Link>
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <p className="text-sm text-red-500 text-center">{errors.submit}</p>
        )}

        {/* Login Button */}
        <AuthButton
          type="submit"
          loading={loading}
          className="mb-6"
        >
           Login
        </AuthButton>

        {/* Divider */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or</span>
          </div>
        </div>

        {/* Social Login Buttons */}
        <div className="space-y-3">
          <SocialButton
            provider="facebook"
            onClick={() => handleSocialLogin('facebook')}
          >
            Login With Facebook
          </SocialButton>
          
          <SocialButton
            provider="google"
            onClick={() => handleSocialLogin('google')}
          >
            Login With Google
          </SocialButton>
        </div>

        {/* Sign Up Link */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            Don't have an account?{' '}
            <Link 
              href="/signup" 
              className="text-[#24BDC7] hover:underline font-medium"
            >
              Sign Up
            </Link>
          </p>
        </div>
      </form>
    </div>
  );
};

export default LoginForm;
